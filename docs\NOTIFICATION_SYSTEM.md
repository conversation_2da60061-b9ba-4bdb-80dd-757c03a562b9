# Local Notification System Documentation

## Overview

This React Native Expo app now includes a complete local notification system that integrates with your SQLite database to alert users when reminders are due. The system is production-ready and follows React Native/Expo best practices.

## Features

✅ **Database Integration**: Automatically reads upcoming reminders from SQLite database  
✅ **Smart Scheduling**: Schedules notifications based on reminder due dates  
✅ **Permission Handling**: <PERSON>per<PERSON> requests and manages notification permissions  
✅ **Automatic Management**: Notifications are automatically created, updated, and cancelled with reminder operations  
✅ **Background Support**: Reschedules notifications when app becomes active  
✅ **TypeScript Support**: Fully typed with comprehensive interfaces  
✅ **Error Handling**: Robust error handling for all operations  
✅ **Production Ready**: Follows best practices and includes proper cleanup  

## Architecture

### Core Components

1. **NotificationService** (`services/notificationService.ts`)
   - Singleton service managing all notification operations
   - Handles permissions, scheduling, and cancellation
   - Integrates with expo-notifications

2. **Notification Types** (`types/notifications.ts`)
   - Comprehensive TypeScript interfaces
   - Error codes and result types
   - Configuration options

3. **ReminderContext Integration** (`contexts/ReminderContext.tsx`)
   - Automatic notification management
   - Schedules notifications on reminder creation
   - Updates notifications on reminder changes
   - Cancels notifications on reminder deletion

4. **Notification Hooks** (`hooks/useNotifications.ts`)
   - App lifecycle management
   - Permission handling utilities
   - Automatic initialization

## Usage Examples

### Basic Usage (Automatic)

The notification system works automatically once integrated. When you create, update, or delete reminders through the ReminderContext, notifications are managed automatically:

```typescript
import { useReminders } from '@/contexts/ReminderContext';

function MyComponent() {
  const { createReminder, updateReminder, deleteReminder } = useReminders();

  // This will automatically schedule a notification
  const handleCreateReminder = async () => {
    const success = await createReminder({
      userId: 1,
      title: "Study for exam",
      description: "Review chapters 1-5",
      dueDate: "2024-01-15T10:00:00.000Z",
      priority: "high"
    });
  };

  // This will automatically update the notification
  const handleUpdateReminder = async (id: number) => {
    const success = await updateReminder(id, {
      dueDate: "2024-01-15T14:00:00.000Z"
    });
  };

  // This will automatically cancel the notification
  const handleDeleteReminder = async (id: number) => {
    const success = await deleteReminder(id);
  };
}
```

### Manual Notification Management

You can also use the NotificationService directly for advanced scenarios:

```typescript
import NotificationService from '@/services/notificationService';

// Schedule a single notification
const scheduleNotification = async (reminder: Reminder) => {
  const result = await NotificationService.scheduleReminderNotification(reminder);
  
  if (result.success) {
    console.log('Notification scheduled:', result.notificationId);
  } else {
    console.error('Failed to schedule:', result.error);
  }
};

// Schedule all user notifications
const scheduleAllNotifications = async (userId: number) => {
  const result = await NotificationService.scheduleAllUserReminders(userId);
  console.log(`Scheduled: ${result.scheduled}, Failed: ${result.failed}`);
};

// Check permissions
const checkPermissions = async () => {
  const status = await NotificationService.getPermissionStatus();
  console.log('Permission status:', status);
};
```

### Permission Management

```typescript
import { useNotificationPermissions } from '@/hooks/useNotifications';

function PermissionComponent() {
  const { checkAndRequestPermissions, getPermissionStatus } = useNotificationPermissions();

  const handleRequestPermissions = async () => {
    const granted = await checkAndRequestPermissions();
    
    if (granted) {
      console.log('Permissions granted!');
    } else {
      console.log('Permissions denied');
    }
  };

  return (
    <Button onPress={handleRequestPermissions} title="Enable Notifications" />
  );
}
```

## Configuration

### App Configuration

The notification system is configured in `app.json`:

```json
{
  "expo": {
    "plugins": [
      [
        "expo-notifications",
        {
          "icon": "./assets/images/notification-icon.png",
          "color": "#ffffff",
          "defaultChannel": "default"
        }
      ]
    ]
  }
}
```

### Service Configuration

You can customize the NotificationService behavior:

```typescript
import { NotificationService } from '@/services/notificationService';

const customService = NotificationService.getInstance({
  defaultChannelId: 'custom-reminders',
  defaultChannelName: 'My Reminders',
  defaultSound: true,
  defaultVibrate: true,
  defaultPriority: 'high'
});
```

## Database Schema

The system works with your existing reminder table structure:

```sql
CREATE TABLE reminders (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  userId INTEGER NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  dueDate TEXT NOT NULL,  -- ISO 8601 format
  isCompleted INTEGER NOT NULL DEFAULT 0,
  priority TEXT NOT NULL DEFAULT 'medium',
  createdAt TEXT NOT NULL,
  updatedAt TEXT NOT NULL
);
```

## Error Handling

The system includes comprehensive error handling:

```typescript
// All operations return success/error results
const result = await NotificationService.scheduleReminderNotification(reminder);

if (!result.success) {
  switch (result.error?.code) {
    case 'PERMISSION_DENIED':
      // Handle permission issues
      break;
    case 'INVALID_DATE':
      // Handle date validation errors
      break;
    case 'PAST_DATE':
      // Handle scheduling for past dates
      break;
    default:
      // Handle other errors
      break;
  }
}
```

## Best Practices

1. **Permission Timing**: Request permissions at appropriate moments, not immediately on app launch
2. **Error Handling**: Always handle notification failures gracefully - don't let them break core functionality
3. **Background Behavior**: The system automatically reschedules notifications when the app becomes active
4. **Testing**: Test on both iOS and Android as notification behavior differs between platforms
5. **User Experience**: Provide clear feedback when notifications are enabled/disabled

## Troubleshooting

### Common Issues

1. **Notifications not appearing**: Check permissions and ensure dates are in the future
2. **Permissions denied**: Guide users to app settings to manually enable notifications
3. **Duplicate notifications**: The system automatically cancels existing notifications before scheduling new ones
4. **Background scheduling**: Notifications are rescheduled when the app becomes active

### Debug Information

Enable debug logging by checking the console for notification-related messages:

```typescript
// The system logs all major operations
console.log('Notification service initialized');
console.log('Scheduled notification for reminder X');
console.log('Cancelled notification for reminder Y');
```

## Platform Differences

### iOS
- Requires explicit permission request
- Supports rich notifications with actions
- Respects system notification settings

### Android
- More permissive notification system
- Supports notification channels
- Different priority levels

The NotificationService handles these differences automatically.
