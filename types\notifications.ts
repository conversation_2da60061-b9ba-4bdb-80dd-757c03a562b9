import { NotificationRequest, NotificationResponse } from 'expo-notifications';

// Notification permission status
export type NotificationPermissionStatus = 'granted' | 'denied' | 'undetermined';

// Notification permission result
export interface NotificationPermissionResult {
  status: NotificationPermissionStatus;
  canAskAgain: boolean;
  granted: boolean;
}

// Reminder notification data
export interface ReminderNotificationData {
  reminderId: number;
  userId: number;
  title: string;
  description?: string;
  priority: 'low' | 'medium' | 'high';
  type: 'reminder';
}

// Notification scheduling result
export interface NotificationScheduleResult {
  success: boolean;
  notificationId?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// Notification service configuration
export interface NotificationServiceConfig {
  defaultChannelId: string;
  defaultChannelName: string;
  defaultChannelDescription: string;
  defaultSound: boolean;
  defaultVibrate: boolean;
  defaultPriority: 'low' | 'normal' | 'high';
}

// Scheduled notification info
export interface ScheduledNotificationInfo {
  identifier: string;
  reminderId: number;
  scheduledTime: Date;
  content: {
    title: string;
    body: string;
    data: ReminderNotificationData;
  };
}

// Notification service error codes
export enum NotificationErrorCode {
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  PERMISSION_REQUEST_FAILED = 'PERMISSION_REQUEST_FAILED',
  SCHEDULING_FAILED = 'SCHEDULING_FAILED',
  CANCELLATION_FAILED = 'CANCELLATION_FAILED',
  INVALID_DATE = 'INVALID_DATE',
  PAST_DATE = 'PAST_DATE',
  DATABASE_ERROR = 'DATABASE_ERROR',
  INITIALIZATION_FAILED = 'INITIALIZATION_FAILED'
}

// Notification service error
export interface NotificationServiceError {
  code: NotificationErrorCode;
  message: string;
  details?: any;
}

// Bulk notification operation result
export interface BulkNotificationResult {
  success: boolean;
  scheduled: number;
  failed: number;
  errors: NotificationServiceError[];
}

// Notification handler callback types
export type NotificationReceivedHandler = (notification: NotificationResponse) => void;
export type NotificationResponseHandler = (response: NotificationResponse) => void;

// Extended notification request with reminder data
export interface ReminderNotificationRequest extends Omit<NotificationRequest, 'content'> {
  content: {
    title: string;
    body: string;
    data: ReminderNotificationData;
    sound?: boolean | string;
    priority?: 'low' | 'normal' | 'high';
    vibrate?: boolean;
  };
}
