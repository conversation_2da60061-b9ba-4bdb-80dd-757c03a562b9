import { Reminder } from '@/types/database';
import {
  BulkNotificationResult,
  NotificationErrorCode,
  NotificationPermissionResult,
  NotificationR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  NotificationResponse<PERSON>andler,
  NotificationScheduleResult,
  NotificationServiceConfig,
  NotificationServiceError,
  ReminderNotificationData,
  ScheduledNotificationInfo
} from '@/types/notifications';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { ReminderDatabaseService } from './reminderDatabase';

// Custom error class for notification service
class NotificationServiceErrorClass extends Error {
  public code: NotificationErrorCode;
  public details?: any;

  constructor(error: { code: NotificationErrorCode; message: string; details?: any }) {
    super(error.message);
    this.name = 'NotificationServiceError';
    this.code = error.code;
    this.details = error.details;
  }
}

// Default notification service configuration
const DEFAULT_CONFIG: NotificationServiceConfig = {
  defaultChannelId: 'reminders',
  defaultChannelName: 'Reminders',
  defaultChannelDescription: 'Notifications for your reminders',
  defaultSound: true,
  defaultVibrate: true,
  defaultPriority: 'high'
};

export class NotificationService {
  private static instance: NotificationService;
  private config: NotificationServiceConfig;
  private isInitialized = false;
  private notificationReceivedListener?: Notifications.Subscription;
  private notificationResponseListener?: Notifications.Subscription;

  private constructor(config: Partial<NotificationServiceConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Get singleton instance of NotificationService
   */
  public static getInstance(config?: Partial<NotificationServiceConfig>): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService(config);
    }
    return NotificationService.instance;
  }

  /**
   * Initialize the notification service
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Set notification handler
      Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: this.config.defaultSound,
          shouldSetBadge: true,
        }),
      });

      // Create notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync(this.config.defaultChannelId, {
          name: this.config.defaultChannelName,
          description: this.config.defaultChannelDescription,
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: this.config.defaultVibrate ? [0, 250, 250, 250] : undefined,
          lightColor: '#3b82f6',
          sound: this.config.defaultSound ? 'default' : undefined,
        });
      }

      this.isInitialized = true;
      console.log('NotificationService initialized successfully');
    } catch (error) {
      console.error('Failed to initialize NotificationService:', error);
      throw new NotificationServiceErrorClass({
        code: NotificationErrorCode.INITIALIZATION_FAILED,
        message: 'Failed to initialize notification service',
        details: error
      });
    }
  }

  /**
   * Check current notification permission status
   */
  public async getPermissionStatus(): Promise<NotificationPermissionResult> {
    try {
      const { status, canAskAgain } = await Notifications.getPermissionsAsync();
      
      return {
        status: status as any,
        canAskAgain,
        granted: status === 'granted'
      };
    } catch (error) {
      console.error('Error getting notification permissions:', error);
      return {
        status: 'undetermined',
        canAskAgain: true,
        granted: false
      };
    }
  }

  /**
   * Request notification permissions
   */
  public async requestPermissions(): Promise<NotificationPermissionResult> {
    try {
      const { status, canAskAgain } = await Notifications.requestPermissionsAsync({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
          allowAnnouncements: true,
        },
      });

      const result: NotificationPermissionResult = {
        status: status as any,
        canAskAgain,
        granted: status === 'granted'
      };

      if (!result.granted) {
        console.warn('Notification permissions not granted:', result);
      }

      return result;
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      throw new NotificationServiceErrorClass({
        code: NotificationErrorCode.PERMISSION_REQUEST_FAILED,
        message: 'Failed to request notification permissions',
        details: error
      });
    }
  }

  /**
   * Ensure notification permissions are granted
   */
  public async ensurePermissions(): Promise<boolean> {
    const currentStatus = await this.getPermissionStatus();
    
    if (currentStatus.granted) {
      return true;
    }

    if (!currentStatus.canAskAgain) {
      console.warn('Cannot ask for notification permissions again');
      return false;
    }

    const requestResult = await this.requestPermissions();
    return requestResult.granted;
  }

  /**
   * Schedule a notification for a reminder
   */
  public async scheduleReminderNotification(reminder: Reminder): Promise<NotificationScheduleResult> {
    try {
      // Ensure service is initialized
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Ensure permissions
      const hasPermissions = await this.ensurePermissions();
      if (!hasPermissions) {
        return {
          success: false,
          error: {
            code: NotificationErrorCode.PERMISSION_DENIED,
            message: 'Notification permissions not granted'
          }
        };
      }

      // Parse due date
      const dueDate = new Date(reminder.dueDate);
      const now = new Date();

      // Check if date is valid
      if (isNaN(dueDate.getTime())) {
        return {
          success: false,
          error: {
            code: NotificationErrorCode.INVALID_DATE,
            message: 'Invalid due date format'
          }
        };
      }

      // Check if date is in the future
      if (dueDate <= now) {
        return {
          success: false,
          error: {
            code: NotificationErrorCode.PAST_DATE,
            message: 'Cannot schedule notification for past date'
          }
        };
      }

      // Cancel existing notification for this reminder
      await this.cancelReminderNotification(reminder.id);

      // Create notification identifier
      const identifier = `reminder_${reminder.id}`;

      // Prepare notification data
      const notificationData: ReminderNotificationData = {
        reminderId: reminder.id,
        userId: reminder.userId,
        title: reminder.title,
        description: reminder.description,
        priority: reminder.priority,
        type: 'reminder'
      };

      // Schedule the notification
      const notificationId = await Notifications.scheduleNotificationAsync({
        identifier,
        content: {
          title: reminder.title,
          body: reminder.description || 'You have a reminder due!',
          data: notificationData,
          sound: this.config.defaultSound,
          priority: this.getPriorityLevel(reminder.priority),
        },
        trigger: {
          date: dueDate,
        },
      });

      console.log(`Scheduled notification for reminder ${reminder.id} at ${dueDate.toISOString()}`);

      return {
        success: true,
        notificationId
      };

    } catch (error) {
      console.error('Error scheduling reminder notification:', error);
      return {
        success: false,
        error: {
          code: NotificationErrorCode.SCHEDULING_FAILED,
          message: 'Failed to schedule notification',
          details: error
        }
      };
    }
  }

  /**
   * Cancel a notification for a specific reminder
   */
  public async cancelReminderNotification(reminderId: number): Promise<boolean> {
    try {
      const identifier = `reminder_${reminderId}`;
      await Notifications.cancelScheduledNotificationAsync(identifier);
      console.log(`Cancelled notification for reminder ${reminderId}`);
      return true;
    } catch (error) {
      console.error('Error cancelling reminder notification:', error);
      return false;
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  public async cancelAllNotifications(): Promise<boolean> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log('Cancelled all scheduled notifications');
      return true;
    } catch (error) {
      console.error('Error cancelling all notifications:', error);
      return false;
    }
  }

  /**
   * Get all scheduled notifications
   */
  public async getScheduledNotifications(): Promise<ScheduledNotificationInfo[]> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      
      return scheduledNotifications
        .filter(notification => notification.content.data?.type === 'reminder')
        .map(notification => ({
          identifier: notification.identifier,
          reminderId: notification.content.data.reminderId,
          scheduledTime: new Date(notification.trigger.value as number),
          content: {
            title: notification.content.title || '',
            body: notification.content.body || '',
            data: notification.content.data as ReminderNotificationData
          }
        }));
    } catch (error) {
      console.error('Error getting scheduled notifications:', error);
      return [];
    }
  }

  /**
   * Schedule notifications for all upcoming reminders for a user
   */
  public async scheduleAllUserReminders(userId: number): Promise<BulkNotificationResult> {
    try {
      // Get all pending reminders for the user
      const result = await ReminderDatabaseService.getPendingReminders(userId);
      
      if (!result.success || !result.data) {
        return {
          success: false,
          scheduled: 0,
          failed: 0,
          errors: [{
            code: NotificationErrorCode.DATABASE_ERROR,
            message: 'Failed to fetch user reminders'
          }]
        };
      }

      const reminders = result.data;
      const now = new Date();
      
      // Filter reminders that are in the future
      const upcomingReminders = reminders.filter(reminder => {
        const dueDate = new Date(reminder.dueDate);
        return dueDate > now;
      });

      let scheduled = 0;
      let failed = 0;
      const errors: NotificationServiceError[] = [];

      // Schedule notification for each upcoming reminder
      for (const reminder of upcomingReminders) {
        const scheduleResult = await this.scheduleReminderNotification(reminder);
        
        if (scheduleResult.success) {
          scheduled++;
        } else {
          failed++;
          if (scheduleResult.error) {
            errors.push({
              code: scheduleResult.error.code as NotificationErrorCode,
              message: `Reminder ${reminder.id}: ${scheduleResult.error.message}`,
              details: scheduleResult.error.details
            });
          }
        }
      }

      console.log(`Bulk scheduling completed: ${scheduled} scheduled, ${failed} failed`);

      return {
        success: failed === 0,
        scheduled,
        failed,
        errors
      };

    } catch (error) {
      console.error('Error in bulk reminder scheduling:', error);
      return {
        success: false,
        scheduled: 0,
        failed: 0,
        errors: [{
          code: NotificationErrorCode.SCHEDULING_FAILED,
          message: 'Bulk scheduling operation failed',
          details: error
        }]
      };
    }
  }

  /**
   * Set up notification event listeners
   */
  public setupNotificationListeners(
    onNotificationReceived?: NotificationReceivedHandler,
    onNotificationResponse?: NotificationResponseHandler
  ): void {
    // Remove existing listeners
    this.removeNotificationListeners();

    // Set up notification received listener
    if (onNotificationReceived) {
      this.notificationReceivedListener = Notifications.addNotificationReceivedListener(
        onNotificationReceived
      );
    }

    // Set up notification response listener
    if (onNotificationResponse) {
      this.notificationResponseListener = Notifications.addNotificationResponseReceivedListener(
        onNotificationResponse
      );
    }

    console.log('Notification listeners set up');
  }

  /**
   * Remove notification event listeners
   */
  public removeNotificationListeners(): void {
    if (this.notificationReceivedListener) {
      this.notificationReceivedListener.remove();
      this.notificationReceivedListener = undefined;
    }

    if (this.notificationResponseListener) {
      this.notificationResponseListener.remove();
      this.notificationResponseListener = undefined;
    }

    console.log('Notification listeners removed');
  }

  /**
   * Convert priority to notification priority level
   */
  private getPriorityLevel(priority: 'low' | 'medium' | 'high'): 'low' | 'normal' | 'high' {
    switch (priority) {
      case 'low':
        return 'low';
      case 'medium':
        return 'normal';
      case 'high':
        return 'high';
      default:
        return 'normal';
    }
  }

  /**
   * Clean up resources
   */
  public cleanup(): void {
    this.removeNotificationListeners();
    this.isInitialized = false;
    console.log('NotificationService cleaned up');
  }
}

// Create and export default instance
export default NotificationService.getInstance();
