import { useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { NotificationResponse } from 'expo-notifications';
import NotificationService from '@/services/notificationService';
import { useUser } from '@/contexts/UserContext';
import { useReminders } from '@/contexts/ReminderContext';

/**
 * Custom hook to manage notification initialization and lifecycle
 */
export function useNotifications() {
  const { user } = useUser();
  const { scheduleAllNotifications } = useReminders();
  const appState = useRef(AppState.currentState);
  const isInitialized = useRef(false);

  // Initialize notification service
  useEffect(() => {
    const initializeNotifications = async () => {
      if (isInitialized.current) return;

      try {
        console.log('Initializing notification service...');
        await NotificationService.initialize();
        
        // Set up notification listeners
        NotificationService.setupNotificationListeners(
          handleNotificationReceived,
          handleNotificationResponse
        );

        isInitialized.current = true;
        console.log('Notification service initialized successfully');
      } catch (error) {
        console.error('Failed to initialize notification service:', error);
      }
    };

    initializeNotifications();

    // Cleanup on unmount
    return () => {
      NotificationService.removeNotificationListeners();
    };
  }, []);

  // Schedule notifications when user is available
  useEffect(() => {
    const scheduleUserNotifications = async () => {
      if (!user || !isInitialized.current) return;

      try {
        console.log('Scheduling notifications for user:', user.id);
        const success = await scheduleAllNotifications(parseInt(user.id));
        
        if (success) {
          console.log('All user notifications scheduled successfully');
        } else {
          console.warn('Some notifications failed to schedule');
        }
      } catch (error) {
        console.error('Failed to schedule user notifications:', error);
      }
    };

    scheduleUserNotifications();
  }, [user, scheduleAllNotifications]);

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active' &&
        user &&
        isInitialized.current
      ) {
        // App has come to the foreground, reschedule notifications
        console.log('App became active, rescheduling notifications...');
        scheduleAllNotifications(parseInt(user.id)).catch(error => {
          console.error('Failed to reschedule notifications on app resume:', error);
        });
      }

      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
    };
  }, [user, scheduleAllNotifications]);

  // Handle notification received while app is in foreground
  const handleNotificationReceived = (notification: NotificationResponse) => {
    console.log('Notification received:', notification);
    
    // You can add custom logic here, such as:
    // - Showing in-app notification
    // - Playing custom sounds
    // - Updating app state
  };

  // Handle notification response (user tapped on notification)
  const handleNotificationResponse = (response: NotificationResponse) => {
    console.log('Notification response:', response);
    
    const notificationData = response.notification.request.content.data;
    
    if (notificationData?.type === 'reminder' && notificationData?.reminderId) {
      // Navigate to the specific reminder
      // You can implement navigation logic here
      console.log('User tapped on reminder notification:', notificationData.reminderId);
      
      // Example: You could use router.push or navigation to go to the reminder
      // router.push(`/reminder/${notificationData.reminderId}`);
    }
  };

  return {
    isInitialized: isInitialized.current,
  };
}

/**
 * Hook to manage notification permissions
 */
export function useNotificationPermissions() {
  const checkAndRequestPermissions = async (): Promise<boolean> => {
    try {
      const hasPermissions = await NotificationService.ensurePermissions();
      return hasPermissions;
    } catch (error) {
      console.error('Failed to check/request notification permissions:', error);
      return false;
    }
  };

  const getPermissionStatus = async () => {
    try {
      return await NotificationService.getPermissionStatus();
    } catch (error) {
      console.error('Failed to get permission status:', error);
      return {
        status: 'undetermined' as const,
        canAskAgain: true,
        granted: false
      };
    }
  };

  return {
    checkAndRequestPermissions,
    getPermissionStatus,
  };
}
